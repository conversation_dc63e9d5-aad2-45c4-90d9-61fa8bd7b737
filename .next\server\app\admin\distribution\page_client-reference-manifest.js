globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/distribution/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./providers/i18n-provider.tsx":{"*":{"id":"(ssr)/./providers/i18n-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./providers/session-provider.tsx":{"*":{"id":"(ssr)/./providers/session-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/requests/page.tsx":{"*":{"id":"(ssr)/./app/requests/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/beneficiaries/page.tsx":{"*":{"id":"(ssr)/./app/beneficiaries/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/reports/page.tsx":{"*":{"id":"(ssr)/./app/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/users/page.tsx":{"*":{"id":"(ssr)/./app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/assistance-types/page.tsx":{"*":{"id":"(ssr)/./app/admin/assistance-types/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/workflow/page.tsx":{"*":{"id":"(ssr)/./app/admin/workflow/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/documents/page.tsx":{"*":{"id":"(ssr)/./app/admin/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/distribution/page.tsx":{"*":{"id":"(ssr)/./app/admin/distribution/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/settings/page.tsx":{"*":{"id":"(ssr)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/audit/page.tsx":{"*":{"id":"(ssr)/./app/admin/audit/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\providers\\i18n-provider.tsx":{"id":"(app-pages-browser)/./providers/i18n-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\providers\\session-provider.tsx":{"id":"(app-pages-browser)/./providers/session-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\requests\\page.tsx":{"id":"(app-pages-browser)/./app/requests/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\beneficiaries\\page.tsx":{"id":"(app-pages-browser)/./app/beneficiaries/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\reports\\page.tsx":{"id":"(app-pages-browser)/./app/reports/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\users\\page.tsx":{"id":"(app-pages-browser)/./app/admin/users/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\assistance-types\\page.tsx":{"id":"(app-pages-browser)/./app/admin/assistance-types/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\workflow\\page.tsx":{"id":"(app-pages-browser)/./app/admin/workflow/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\documents\\page.tsx":{"id":"(app-pages-browser)/./app/admin/documents/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\distribution\\page.tsx":{"id":"(app-pages-browser)/./app/admin/distribution/page.tsx","name":"*","chunks":["app/admin/distribution/page","static/chunks/app/admin/distribution/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\settings\\page.tsx":{"id":"(app-pages-browser)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\audit\\page.tsx":{"id":"(app-pages-browser)/./app/admin/audit/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\":[],"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\page":[],"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\page":[],"C:\\Users\\<USER>\\OneDrive\\Documents\\VCode projects\\zakat-deepagent\\zakat_management_system\\app\\app\\admin\\distribution\\page":[]}}